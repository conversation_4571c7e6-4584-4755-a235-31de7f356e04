<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Handwriting Features</title>
    <style>
        body {
            font-family: 'Indie Flower', cursive;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .feature-demo {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .feature-demo h3 {
            margin-top: 0;
            color: #333;
        }
        .demo-text {
            font-size: 18px;
            line-height: 1.6;
            margin: 10px 0;
        }
        
        /* Stroke Thickness Variation */
        .stroke-thickness .char {
            display: inline-block;
        }
        .stroke-thickness .char:nth-child(2n) {
            font-weight: 600;
            text-shadow: 0 0 0.5px currentColor;
        }
        .stroke-thickness .char:nth-child(3n) {
            font-weight: 300;
        }
        .stroke-thickness .char:nth-child(5n) {
            font-weight: 700;
            text-shadow: 0 0 1px currentColor;
        }
        
        /* Baseline Imperfection */
        .baseline-imperfection .char {
            display: inline-block;
        }
        .baseline-imperfection .char:nth-child(2n) {
            transform: translateY(-1px);
        }
        .baseline-imperfection .char:nth-child(3n) {
            transform: translateY(2px);
        }
        .baseline-imperfection .char:nth-child(5n) {
            transform: translateY(-2px);
        }
        .baseline-imperfection .char:nth-child(7n) {
            transform: translateY(1px);
        }
        
        /* Spacing Inconsistency */
        .spacing-inconsistency .char {
            display: inline-block;
        }
        .spacing-inconsistency .char:nth-child(2n) {
            letter-spacing: 1px;
        }
        .spacing-inconsistency .char:nth-child(3n) {
            letter-spacing: -0.5px;
        }
        .spacing-inconsistency .char:nth-child(5n) {
            letter-spacing: 2px;
        }
        .spacing-inconsistency .space {
            width: 0.8em;
        }
        .spacing-inconsistency .space:nth-child(2n) {
            width: 1.2em;
        }
        .spacing-inconsistency .space:nth-child(3n) {
            width: 0.5em;
        }
        
        /* Combined Effects */
        .combined .char {
            display: inline-block;
        }
        .combined .char:nth-child(2n) {
            font-weight: 600;
            transform: translateY(-1px);
            letter-spacing: 1px;
        }
        .combined .char:nth-child(3n) {
            font-weight: 300;
            transform: translateY(2px);
            letter-spacing: -0.5px;
        }
        .combined .char:nth-child(5n) {
            font-weight: 700;
            transform: translateY(-2px);
            letter-spacing: 2px;
            text-shadow: 0 0 1px currentColor;
        }
        .combined .char:nth-child(7n) {
            transform: translateY(1px) rotate(0.5deg);
        }
        .combined .space {
            width: 0.8em;
        }
        .combined .space:nth-child(2n) {
            width: 1.2em;
        }
        .combined .space:nth-child(3n) {
            width: 0.5em;
        }
    </style>
    <link href="https://fonts.googleapis.com/css2?family=Indie+Flower&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <h1>Handwriting Features Test</h1>
        <p>This page demonstrates the three new handwriting features that make text look more naturally messy and realistic.</p>
        
        <div class="feature-demo">
            <h3>1. Normal Text (Baseline)</h3>
            <div class="demo-text">
                This is normal text without any handwriting effects applied.
            </div>
        </div>
        
        <div class="feature-demo">
            <h3>2. Stroke Thickness Variation</h3>
            <p>Some letters appear bolder or lighter, simulating natural pressure changes while writing.</p>
            <div class="demo-text stroke-thickness" id="stroke-demo">
                This text shows dynamic line weight changes for realistic handwriting.
            </div>
        </div>
        
        <div class="feature-demo">
            <h3>3. Baseline Imperfection</h3>
            <p>Text doesn't sit perfectly straight on an invisible line; some letters are slightly higher or lower.</p>
            <div class="demo-text baseline-imperfection" id="baseline-demo">
                This text demonstrates imperfect baseline positioning for natural feel.
            </div>
        </div>
        
        <div class="feature-demo">
            <h3>4. Spacing Inconsistency</h3>
            <p>The spaces between letters and words are not perfectly uniform, typical in handwritten notes.</p>
            <div class="demo-text spacing-inconsistency" id="spacing-demo">
                This text shows irregular letter and word spacing variations.
            </div>
        </div>
        
        <div class="feature-demo">
            <h3>5. All Effects Combined</h3>
            <p>All three effects working together for the most realistic messy handwriting appearance.</p>
            <div class="demo-text combined" id="combined-demo">
                This text combines all three effects for perfect messy handwriting simulation!
            </div>
        </div>
    </div>
    
    <script>
        function wrapCharacters(elementId) {
            const element = document.getElementById(elementId);
            const text = element.textContent;
            element.innerHTML = '';
            
            for (let i = 0; i < text.length; i++) {
                const char = text[i];
                if (char === ' ') {
                    const space = document.createElement('span');
                    space.className = 'space';
                    space.innerHTML = '&nbsp;';
                    element.appendChild(space);
                } else {
                    const span = document.createElement('span');
                    span.className = 'char';
                    span.textContent = char;
                    element.appendChild(span);
                }
            }
        }
        
        // Wrap characters for all demo elements
        wrapCharacters('stroke-demo');
        wrapCharacters('baseline-demo');
        wrapCharacters('spacing-demo');
        wrapCharacters('combined-demo');
    </script>
</body>
</html>
