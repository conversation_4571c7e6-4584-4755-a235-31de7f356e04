import { useState, useRef, useEffect, useMemo, useCallback, memo } from 'react';
import { Textarea } from '@/components/ui/textarea';
import { Card } from '@/components/ui/card';
import { TypographyConfig } from '@/components/ui/typography-config';
import { LaTeXRenderer, hasLaTeX } from '@/components/ui/latex-renderer';
import { EnhancedToolbar } from '@/components/ui/enhanced-toolbar';
import { FileText } from 'lucide-react';
import html2pdf from 'html2pdf.js';
import html2canvas from 'html2canvas';
import { motion } from 'motion/react';
import '../styles/handwriting-effects.css';
import {
  loadCustomFont,
  createFontFaceCSS,
  injectFontCSS,
  cleanupFont,
  CustomFont,
  getFontFormatDescription,
  formatFileSize
} from '@/utils/fontManager';

// Memoized page component to prevent unnecessary re-renders
const MemoizedPage = memo(({
  pageText,
  index,
  renderPaperBackground,
  totalPages,
  showPageNumbers
}: {
  pageText: string;
  index: number;
  renderPaperBackground: (pageText: string) => JSX.Element;
  totalPages: number;
  showPageNumbers: boolean;
}) => (
  <div
    key={index}
    data-page-index={index}
    className="w-full shadow-lg rounded-lg overflow-hidden bg-white relative"
    style={{
      aspectRatio: '210/297', // A4 ratio
      minHeight: '600px', // Increased for more realistic page size
      maxWidth: '100%',
      width: '100%'
    }}
  >
    {renderPaperBackground(pageText)}
    {totalPages > 1 && showPageNumbers && (
      <div className="absolute bottom-4 right-4 text-xs text-neutral-400 bg-white/80 px-2 py-1 rounded z-10">
        Page {index + 1}
      </div>
    )}
  </div>
));

const HandwritingEditor = () => {
  // Initialize with defaults first, then load from localStorage asynchronously
  const [text, setText] = useState('');
  const [selectedFont, setSelectedFont] = useState('handwriting');
  const [background, setBackground] = useState('lined');

  const [typographyConfig, setTypographyConfig] = useState<TypographyConfig>({
    fontSize: 18,
    lineHeight: 1.6,
    letterSpacing: 0,
    wordSpacing: 0,
    textColor: '#000000',
    marginTop: 10,
    marginBottom: 10,
    marginLeft: 10,
    marginRight: 10,
    handwritingEffects: {
      shadowEnabled: false,
      shadowIntensity: 30,
      shadowBlur: 2,
      shadowOffsetX: 1,
      shadowOffsetY: 1,
      jitterEnabled: false,
      jitterIntensity: 20,
      pressureVariation: false,
      pressureIntensity: 15,
      strokeThicknessVariation: false,
      strokeThicknessIntensity: 25,
      baselineImperfection: false,
      baselineIntensity: 30,
      spacingInconsistency: false,
      spacingInconsistencyIntensity: 20
    }
  });

  const [showPageNumbers, setShowPageNumbers] = useState(false);
  const [customBackground, setCustomBackground] = useState('');

  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [textPosition, setTextPosition] = useState({ x: 0, y: 0 });
  const [isLoaded, setIsLoaded] = useState(false);

  // Custom fonts state
  const [customFonts, setCustomFonts] = useState<CustomFont[]>([]);

  const paperRef = useRef<HTMLDivElement>(null);

  // Local font option interface that extends the base one
  interface LocalFontOption {
    value: string;
    label: string;
    class: string;
    isCustom?: boolean;
    fontUrl?: string;
  }

  // Load from localStorage asynchronously after mount to prevent blocking initial render
  useEffect(() => {
    const loadFromStorage = async () => {
      try {
        // Use requestIdleCallback if available, otherwise setTimeout
        const scheduleWork = (callback: () => void) => {
          if ('requestIdleCallback' in window) {
            requestIdleCallback(callback);
          } else {
            setTimeout(callback, 0);
          }
        };

        scheduleWork(() => {
          const savedText = localStorage.getItem('handwriting-editor-text');
          const savedFont = localStorage.getItem('handwriting-editor-font');
          const savedBackground = localStorage.getItem('handwriting-editor-background');
          const savedTypography = localStorage.getItem('handwriting-editor-typography');
          const savedPageNumbers = localStorage.getItem('handwriting-editor-show-page-numbers');
          const savedCustomBg = localStorage.getItem('handwriting-editor-custom-background');
          const savedTextPosition = localStorage.getItem('handwriting-editor-text-position');

          if (savedText) setText(savedText);
          if (savedFont) setSelectedFont(savedFont);
          if (savedBackground) setBackground(savedBackground);
          if (savedTypography) {
            try {
              const parsedConfig = JSON.parse(savedTypography);
              // Migrate old config to include handwriting effects if missing
              if (!parsedConfig.handwritingEffects) {
                parsedConfig.handwritingEffects = {
                  shadowEnabled: false,
                  shadowIntensity: 30,
                  shadowBlur: 2,
                  shadowOffsetX: 1,
                  shadowOffsetY: 1,
                  jitterEnabled: false,
                  jitterIntensity: 20,
                  pressureVariation: false,
                  pressureIntensity: 15,
                  strokeThicknessVariation: false,
                  strokeThicknessIntensity: 25,
                  baselineImperfection: false,
                  baselineIntensity: 30,
                  spacingInconsistency: false,
                  spacingInconsistencyIntensity: 20
                };
              } else {
                // Migrate existing config to include new features if missing
                const effects = parsedConfig.handwritingEffects;
                if (effects.strokeThicknessVariation === undefined) {
                  effects.strokeThicknessVariation = false;
                  effects.strokeThicknessIntensity = 25;
                }
                if (effects.baselineImperfection === undefined) {
                  effects.baselineImperfection = false;
                  effects.baselineIntensity = 30;
                }
                if (effects.spacingInconsistency === undefined) {
                  effects.spacingInconsistency = false;
                  effects.spacingInconsistencyIntensity = 20;
                }
              }
              setTypographyConfig(parsedConfig);
            } catch (e) {
              console.warn('Failed to parse typography config:', e);
            }
          }
          if (savedPageNumbers) setShowPageNumbers(savedPageNumbers === 'true');
          if (savedCustomBg) setCustomBackground(savedCustomBg);
          if (savedTextPosition) {
            try {
              setTextPosition(JSON.parse(savedTextPosition));
            } catch (e) {
              console.warn('Failed to parse text position:', e);
            }
          }

          setIsLoaded(true);
        });
      } catch (error) {
        console.warn('Failed to load from localStorage:', error);
        setIsLoaded(true);
      }
    };

    loadFromStorage();
  }, []);

  // Debounced localStorage save - only save after initial load to prevent saving defaults
  useEffect(() => {
    if (!isLoaded) return; // Don't save until we've loaded from localStorage

    const timeoutId = setTimeout(() => {
      // Use requestIdleCallback for non-blocking localStorage operations
      const saveToStorage = () => {
        try {
          const batch = {
            'handwriting-editor-text': text,
            'handwriting-editor-font': selectedFont,
            'handwriting-editor-background': background,
            'handwriting-editor-typography': JSON.stringify(typographyConfig),
            'handwriting-editor-text-position': JSON.stringify(textPosition),
            'handwriting-editor-show-page-numbers': showPageNumbers.toString(),
            'handwriting-editor-custom-background': customBackground
          };

          Object.entries(batch).forEach(([key, value]) => {
            localStorage.setItem(key, value);
          });
        } catch (error) {
          console.warn('Failed to save to localStorage:', error);
        }
      };

      if ('requestIdleCallback' in window) {
        requestIdleCallback(saveToStorage);
      } else {
        setTimeout(saveToStorage, 0);
      }
    }, 500); // Increased debounce to 500ms for better performance

    return () => clearTimeout(timeoutId);
  }, [text, selectedFont, background, typographyConfig, textPosition, showPageNumbers, customBackground, isLoaded]);

  // Cleanup custom fonts on unmount
  useEffect(() => {
    return () => {
      customFonts.forEach(font => {
        cleanupFont(font);
      });
    };
  }, [customFonts]);

  // Combine built-in fonts with custom fonts
  const fontOptions = useMemo((): LocalFontOption[] => {
    const builtInFonts: LocalFontOption[] = [
      { value: 'handwriting', label: 'Kalam (Default)', class: 'font-handwriting' },
      { value: 'handwriting-alt', label: 'Caveat', class: 'font-handwriting-alt' },
      { value: 'handwriting-dance', label: 'Dancing Script', class: 'font-handwriting-dance' },
      { value: 'handwriting-architect', label: 'Architects Daughter', class: 'font-handwriting-architect' },
      { value: 'handwriting-indie', label: 'Indie Flower', class: 'font-handwriting-indie' },
    ];

    const customFontOptions: LocalFontOption[] = customFonts.map(font => ({
      value: font.id,
      label: `${font.name} (${getFontFormatDescription(font.format)})`,
      class: `custom-font-${font.id}`,
      isCustom: true,
      fontUrl: font.url
    }));

    return [...builtInFonts, ...customFontOptions];
  }, [customFonts]);

  const selectedFontClass = useMemo(() => {
    const selectedFontOption = fontOptions.find(f => f.value === selectedFont);
    if (selectedFontOption?.isCustom) {
      // For custom fonts, we'll use the font family directly in CSS
      return `custom-font-${selectedFont}`;
    }
    return selectedFontOption?.class || 'font-handwriting';
  }, [selectedFont, fontOptions]);

  // Memoize LaTeX detection to avoid repeated regex checks
  const textHasLaTeX = useMemo(() => hasLaTeX(text), [text]);

  // Debounced text handler to reduce re-renders while typing
  const handleTextChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setText(e.target.value);
  }, []);

  // Memoize expensive calculations
  const memoizedSplitText = useMemo(() => {
    if (!text.trim()) return [''];

    // More realistic page calculations based on A4 dimensions
    // A4 is 210mm x 297mm, with typical margins of 25mm
    // Usable area: 160mm x 247mm
    const baseCharsPerLine = 65; // Realistic for handwriting
    const baseLinesPerPage = 30; // Realistic for A4 with margins

    // Adjust based on font size
    const fontSizeRatio = typographyConfig.fontSize / 18; // 18px is our base
    const lineHeightRatio = typographyConfig.lineHeight / 1.6; // 1.6 is our base

    const charsPerLine = Math.floor(baseCharsPerLine / fontSizeRatio);
    const linesPerPage = Math.floor(baseLinesPerPage / (fontSizeRatio * lineHeightRatio));

    const lines = text.split('\n');
    const pages = [];
    let currentPage = '';
    let currentLineCount = 0;

    for (const line of lines) {
      // Calculate how many visual lines this text line will take
      const visualLines = Math.max(1, Math.ceil(line.length / charsPerLine));

      // Check if adding this line would exceed page capacity
      if (currentLineCount + visualLines > linesPerPage && currentPage.trim()) {
        pages.push(currentPage.trim());
        currentPage = line + '\n';
        currentLineCount = visualLines;
      } else {
        currentPage += line + '\n';
        currentLineCount += visualLines;
      }
    }

    // Add the last page if it has content
    if (currentPage.trim()) {
      pages.push(currentPage.trim());
    }

    return pages.length > 0 ? pages : [''];
  }, [text, typographyConfig.fontSize, typographyConfig.lineHeight]);

  const handleCustomBackgroundUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setCustomBackground(result);
        setBackground('custom');
      };
      reader.readAsDataURL(file);
    }
  };

  const handleCustomFontUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      // Load and validate the font
      const customFont = await loadCustomFont(file);

      // Create and inject CSS
      const fontCSS = createFontFaceCSS(customFont);
      injectFontCSS(fontCSS, customFont.id);

      // Add to custom fonts list
      setCustomFonts(prev => [...prev, customFont]);

      // Automatically select the new font
      setSelectedFont(customFont.id);

      // Show success message
      alert(`Font "${customFont.name}" uploaded successfully! (${formatFileSize(file.size)})`);

    } catch (error) {
      console.error('Font upload error:', error);
      alert(`Failed to upload font: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // Clear the input
    event.target.value = '';
  };

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    setIsDragging(true);
    setDragOffset({
      x: e.clientX - textPosition.x,
      y: e.clientY - textPosition.y
    });
  }, [textPosition.x, textPosition.y]);

  // Throttled mouse move to prevent excessive updates
  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (isDragging) {
      // Use requestAnimationFrame for smooth updates and prevent excessive calls
      if (!window.requestAnimationFrame) return;

      requestAnimationFrame(() => {
        setTextPosition({
          x: e.clientX - dragOffset.x,
          y: e.clientY - dragOffset.y
        });
      });
    }
  }, [isDragging, dragOffset.x, dragOffset.y]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  const resetTextPosition = useCallback(() => {
    setTextPosition({ x: 0, y: 0 });
  }, []);

  const generatePDF = async () => {
    if (!paperRef.current || !text.trim()) {
      alert('Please write some text before generating PDF');
      return;
    }

    const element = paperRef.current;
    const opt = {
      margin: 0,
      filename: 'handwritten-note.pdf',
      image: { type: 'jpeg', quality: 0.98 },
      html2canvas: {
        scale: 2,
        useCORS: true,
        backgroundColor: '#ffffff'
      },
      jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
    };

    try {
      await html2pdf().set(opt).from(element).save();
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Error generating PDF. Please try again.');
    }
  };

  const generateImage = async (quality: 'standard' | 'high' | 'print' = 'high') => {
    if (!paperRef.current || !text.trim()) {
      alert('Please write some text before generating image');
      return;
    }

    // Quality settings - start with lower scale for testing
    const qualitySettings = {
      standard: { scale: 1, format: 'png', quality: 0.9 },
      high: { scale: 2, format: 'png', quality: 1.0 },
      print: { scale: 3, format: 'png', quality: 1.0 }
    };

    const settings = qualitySettings[quality];

    try {
      console.log('Starting image generation...');

      // Use the main paper container directly
      const targetElement = paperRef.current;

      console.log('Target element:', targetElement);
      console.log('Element dimensions:', {
        width: targetElement.offsetWidth,
        height: targetElement.offsetHeight,
        scrollWidth: targetElement.scrollWidth,
        scrollHeight: targetElement.scrollHeight
      });

      // Very basic html2canvas configuration to start
      const canvas = await html2canvas(targetElement, {
        scale: settings.scale,
        useCORS: true,
        allowTaint: true, // Allow cross-origin content
        backgroundColor: '#ffffff',
        logging: true,
        width: targetElement.scrollWidth,
        height: targetElement.scrollHeight,
        x: 0,
        y: 0,
        scrollX: 0,
        scrollY: 0
      });

      console.log('Canvas generated successfully:', {
        width: canvas.width,
        height: canvas.height
      });

      // Check if canvas has content
      const ctx = canvas.getContext('2d');
      const imageData = ctx?.getImageData(0, 0, canvas.width, canvas.height);
      const hasContent = imageData?.data.some((pixel, index) => {
        // Check if any pixel is not white (skip alpha channel)
        if (index % 4 === 3) return false; // Skip alpha
        return pixel !== 255;
      });

      if (!hasContent) {
        console.warn('Canvas appears to be blank');
        alert('Generated image appears to be blank. This might be due to complex content. Try simplifying the text or disabling some effects.');
        return;
      }

      // Create download
      const link = document.createElement('a');
      link.download = `handwritten-note-${quality}-${Date.now()}.png`;
      link.href = canvas.toDataURL('image/png');

      // Trigger download
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      console.log('Image download triggered successfully');

    } catch (error) {
      console.error('Error generating image:', error);
      alert(`Error generating image: ${error.message}. Try using simpler text or disabling some handwriting effects.`);
    }
  };

  // Generate handwriting effects CSS
  const generateHandwritingEffects = useCallback((effects: typeof typographyConfig.handwritingEffects) => {
    const styles: React.CSSProperties = {};

    // Shadow effects
    if (effects.shadowEnabled) {
      const shadowOpacity = effects.shadowIntensity / 100;
      const shadowColor = `rgba(0, 0, 0, ${shadowOpacity * 0.3})`;
      styles.textShadow = `${effects.shadowOffsetX}px ${effects.shadowOffsetY}px ${effects.shadowBlur}px ${shadowColor}`;
    }

    // Jitter effects using CSS transforms and filters
    if (effects.jitterEnabled) {
      const jitterAmount = effects.jitterIntensity / 100;
      // Create subtle rotation and skew for natural imperfections
      const rotation = (Math.random() - 0.5) * jitterAmount * 2; // -1 to 1 degree
      const skewX = (Math.random() - 0.5) * jitterAmount * 1; // -0.5 to 0.5 degree
      const skewY = (Math.random() - 0.5) * jitterAmount * 0.5; // -0.25 to 0.25 degree

      // Add subtle blur for imperfection
      if (jitterAmount > 0.3) {
        styles.filter = `blur(${jitterAmount * 0.3}px)`;
      }

      // Store jitter transforms separately to combine with position transform
      styles['--jitter-transform'] = `rotate(${rotation}deg) skewX(${skewX}deg) skewY(${skewY}deg)`;
    }

    // Pressure variation effects
    if (effects.pressureVariation) {
      const pressureAmount = effects.pressureIntensity / 100;
      // Vary opacity slightly to simulate pressure changes
      const opacityVariation = 1 - (pressureAmount * 0.2); // Reduce opacity by up to 20%
      styles.opacity = Math.max(0.7, opacityVariation);

      // Add subtle font weight variation
      const weightVariation = Math.floor(400 + (pressureAmount * 200)); // 400-600
      styles.fontWeight = weightVariation;
    }

    return styles;
  }, []);

  // Memoize text style to prevent recalculation
  const textStyle = useMemo(() => {
    const handwritingEffects = generateHandwritingEffects(typographyConfig.handwritingEffects);
    // Don't apply jitter transform here if we're doing per-character jitter
    const jitterTransform = typographyConfig.handwritingEffects.jitterEnabled ? '' : (handwritingEffects['--jitter-transform'] as string || '');

    // For custom fonts, apply the font family directly
    const selectedFontOption = fontOptions.find(f => f.value === selectedFont);
    const customFontFamily = selectedFontOption?.isCustom ?
      customFonts.find(f => f.id === selectedFont)?.family : undefined;

    return {
      fontSize: `${typographyConfig.fontSize}px`,
      lineHeight: background === 'lined' ? '25px' : typographyConfig.lineHeight,
      letterSpacing: `${typographyConfig.letterSpacing}px`,
      wordSpacing: `${typographyConfig.wordSpacing}px`,
      color: background === 'blueprint' ? '#ffffff' : typographyConfig.textColor,
      transform: `translate(${textPosition.x}px, ${textPosition.y}px) ${jitterTransform}`,
      cursor: isDragging ? 'grabbing' : 'grab',
      fontFamily: customFontFamily || undefined,
      ...handwritingEffects
    };
  }, [
    typographyConfig.fontSize,
    typographyConfig.lineHeight,
    typographyConfig.letterSpacing,
    typographyConfig.wordSpacing,
    typographyConfig.textColor,
    typographyConfig.handwritingEffects,
    background,
    textPosition.x,
    textPosition.y,
    isDragging,
    generateHandwritingEffects,
    selectedFont,
    fontOptions,
    customFonts
  ]);

  // Enhanced text rendering with per-character jitter effects
  const renderTextContent = useCallback((pageText: string) => {
    const displayText = pageText || 'Your handwritten text will appear here...';

    // Check if any per-character effects are enabled
    const shouldApplyPerCharacterEffects = pageText && (
      typographyConfig.handwritingEffects.jitterEnabled ||
      typographyConfig.handwritingEffects.strokeThicknessVariation ||
      typographyConfig.handwritingEffects.baselineImperfection ||
      typographyConfig.handwritingEffects.spacingInconsistency
    );

    const content = hasLaTeX(displayText) ? (
      <LaTeXRenderer
        text={displayText}
        className={`${selectedFontClass} whitespace-pre-wrap`}
        style={textStyle}
      />
    ) : shouldApplyPerCharacterEffects ? (
      <div
        className={`${selectedFontClass} whitespace-pre-wrap`}
        style={{
          ...textStyle,
          // Remove jitter transform from main style since we're applying it per character
          transform: `translate(${textPosition.x}px, ${textPosition.y}px)`
        }}
      >
        {displayText.split('').map((char, index) => {
          if (char === '\n') return <br key={index} />;

          // Handle spaces with spacing inconsistency
          if (char === ' ') {
            let spaceStyle = {};
            if (typographyConfig.handwritingEffects.spacingInconsistency) {
              const spacingAmount = typographyConfig.handwritingEffects.spacingInconsistencyIntensity / 100;
              const seed = index * 0.618033988749;
              const spaceVariation = 0.5 + (Math.sin(seed * 1.7) * 0.5 + 0.5) * spacingAmount * 1.5; // 0.5x to 2x normal spacing
              spaceStyle = {
                display: 'inline-block',
                width: `${spaceVariation}em`
              };
            }
            return <span key={index} style={spaceStyle}> </span>;
          }

          // Seeded randomness for consistency
          const seed = index * 0.618033988749; // Golden ratio for better distribution

          // Jitter effects
          let jitterTransform = '';
          if (typographyConfig.handwritingEffects.jitterEnabled) {
            const jitterAmount = typographyConfig.handwritingEffects.jitterIntensity / 100;
            const rotation = (Math.sin(seed) * 0.5) * jitterAmount * 2;
            const translateX = (Math.cos(seed * 1.1) * 0.5) * jitterAmount * 1.5;
            const translateY = (Math.sin(seed * 1.3) * 0.5) * jitterAmount * 1.5;
            jitterTransform = `rotate(${rotation}deg) translate(${translateX}px, ${translateY}px)`;
          }

          // Baseline imperfection
          let baselineTransform = '';
          if (typographyConfig.handwritingEffects.baselineImperfection) {
            const baselineAmount = typographyConfig.handwritingEffects.baselineIntensity / 100;
            const baselineOffset = (Math.sin(seed * 0.5) * 0.5) * baselineAmount * 3; // Up to 3px vertical offset
            baselineTransform = `translateY(${baselineOffset}px)`;
          }

          // Combine transforms
          const combinedTransform = [jitterTransform, baselineTransform].filter(Boolean).join(' ');

          // Pressure variation
          let pressureStyle = {};
          if (typographyConfig.handwritingEffects.pressureVariation) {
            const pressureAmount = typographyConfig.handwritingEffects.pressureIntensity / 100;
            const pressureVariation = 0.8 + (Math.cos(seed * 0.7) * 0.5 + 0.5) * 0.4; // 0.8 to 1.2
            const weightVariation = 400 + (Math.sin(seed * 0.9) * 0.5 + 0.5) * pressureAmount * 200;
            pressureStyle = {
              opacity: pressureVariation,
              fontWeight: Math.floor(weightVariation)
            };
          }

          // Stroke thickness variation
          let strokeStyle = {};
          if (typographyConfig.handwritingEffects.strokeThicknessVariation) {
            const strokeAmount = typographyConfig.handwritingEffects.strokeThicknessIntensity / 100;
            const strokeVariation = 0.7 + (Math.cos(seed * 1.9) * 0.5 + 0.5) * strokeAmount * 0.6; // 0.7x to 1.3x thickness
            const strokeWeight = Math.floor(300 + strokeVariation * 400); // 300-700 font weight range
            strokeStyle = {
              fontWeight: strokeWeight,
              textShadow: strokeVariation > 1 ? `0 0 ${(strokeVariation - 1) * 0.5}px currentColor` : undefined
            };
          }

          // Letter spacing inconsistency
          let letterSpacingStyle = {};
          if (typographyConfig.handwritingEffects.spacingInconsistency) {
            const spacingAmount = typographyConfig.handwritingEffects.spacingInconsistencyIntensity / 100;
            const letterSpacingVariation = -0.5 + (Math.sin(seed * 2.1) * 0.5 + 0.5) * spacingAmount * 2; // -0.5px to 1.5px
            letterSpacingStyle = {
              letterSpacing: `${letterSpacingVariation}px`
            };
          }

          return (
            <span
              key={index}
              style={{
                display: 'inline-block',
                transform: combinedTransform || undefined,
                transformOrigin: 'center center',
                ...pressureStyle,
                ...strokeStyle,
                ...letterSpacingStyle
              }}
            >
              {char}
            </span>
          );
        })}
      </div>
    ) : (
      <div
        className={`${selectedFontClass} whitespace-pre-wrap`}
        style={textStyle}
      >
        {displayText}
      </div>
    );

    return (
      <div
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
        className="relative"
      >
        {content}
      </div>
    );
  }, [
    textStyle,
    selectedFontClass,
    handleMouseDown,
    handleMouseMove,
    handleMouseUp,
    typographyConfig.handwritingEffects.jitterEnabled,
    typographyConfig.handwritingEffects.jitterIntensity,
    typographyConfig.handwritingEffects.pressureVariation,
    typographyConfig.handwritingEffects.pressureIntensity,
    typographyConfig.handwritingEffects.strokeThicknessVariation,
    typographyConfig.handwritingEffects.strokeThicknessIntensity,
    typographyConfig.handwritingEffects.baselineImperfection,
    typographyConfig.handwritingEffects.baselineIntensity,
    typographyConfig.handwritingEffects.spacingInconsistency,
    typographyConfig.handwritingEffects.spacingInconsistencyIntensity,
    textPosition.x,
    textPosition.y
  ]);



  const renderPaperBackground = useCallback((pageText: string) => {
    const textContent = renderTextContent(pageText);
    const marginStyle = {
      paddingTop: `${typographyConfig.marginTop}px`,
      paddingBottom: `${typographyConfig.marginBottom}px`,
      paddingLeft: `${typographyConfig.marginLeft}px`,
      paddingRight: `${typographyConfig.marginRight}px`
    };

    switch (background) {
      case 'plain':
        return (
          <div className="w-full h-full relative bg-white" style={marginStyle}>
            {textContent}
          </div>
        );

      case 'warm-white':
        return (
          <div
            className="w-full h-full relative"
            style={{
              ...marginStyle,
              background: 'linear-gradient(135deg, #fefcf3 0%, #fdf8e8 50%, #fcf5e0 100%)',
              boxShadow: 'inset 0 0 100px rgba(255, 193, 7, 0.1)'
            }}
          >
            {textContent}
          </div>
        );

      case 'cool-white':
        return (
          <div
            className="w-full h-full relative"
            style={{
              ...marginStyle,
              background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 50%, #e2e8f0 100%)',
              boxShadow: 'inset 0 0 100px rgba(59, 130, 246, 0.08)'
            }}
          >
            {textContent}
          </div>
        );

      case 'fluorescent':
        return (
          <div
            className="w-full h-full relative"
            style={{
              ...marginStyle,
              background: 'linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 50%, #d1fae5 100%)',
              boxShadow: 'inset 0 0 80px rgba(34, 197, 94, 0.06)'
            }}
          >
            {textContent}
          </div>
        );

      case 'aged-paper':
        return (
          <div
            className="w-full h-full relative"
            style={{
              ...marginStyle,
              background: `
                radial-gradient(circle at 20% 80%, rgba(139, 69, 19, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(139, 69, 19, 0.02) 0%, transparent 50%),
                linear-gradient(135deg, #faf7f0 0%, #f5f0e8 50%, #f0ebe3 100%)
              `,
              boxShadow: 'inset 0 0 120px rgba(139, 69, 19, 0.08)'
            }}
          >
            {textContent}
          </div>
        );

      case 'textured-white':
        return (
          <div
            className="w-full h-full relative"
            style={{
              ...marginStyle,
              background: `
                radial-gradient(circle at 25% 25%, rgba(0, 0, 0, 0.01) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(0, 0, 0, 0.01) 0%, transparent 50%),
                linear-gradient(45deg, #ffffff 25%, #fefefe 25%, #fefefe 50%, #ffffff 50%, #ffffff 75%, #fefefe 75%, #fefefe)
              `,
              backgroundSize: '4px 4px, 4px 4px, 8px 8px',
              backgroundColor: '#ffffff'
            }}
          >
            {textContent}
          </div>
        );

      case 'custom':
        return (
          <div
            className="w-full h-full relative"
            style={{
              ...marginStyle,
              backgroundImage: customBackground ? `url(${customBackground})` : 'none',
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              backgroundRepeat: 'no-repeat',
              backgroundColor: customBackground ? 'transparent' : '#ffffff'
            }}
          >
            {/* Very light overlay for better text readability without graying out */}
            {customBackground && (
              <div className="absolute inset-0 bg-white/5"></div>
            )}

            {/* Text content */}
            <div className="relative z-10">
              {textContent}
            </div>
          </div>
        );

      case 'dots':
        return (
          <div
            className="w-full h-full relative bg-white"
            style={{
              ...marginStyle,
              backgroundImage: `radial-gradient(circle, #d1d5db 1px, transparent 1px)`,
              backgroundSize: '20px 20px',
              backgroundPosition: '10px 10px'
            }}
          >
            {textContent}
          </div>
        );

      case 'graph':
        return (
          <div
            className="w-full h-full relative bg-white"
            style={{
              ...marginStyle,
              backgroundImage: `
                linear-gradient(to right, #e5e7eb 1px, transparent 1px),
                linear-gradient(to bottom, #e5e7eb 1px, transparent 1px)
              `,
              backgroundSize: '20px 20px'
            }}
          >
            {textContent}
          </div>
        );

      case 'music':
        return (
          <div
            className="w-full h-full relative bg-white"
            style={{
              ...marginStyle,
              backgroundImage: `
                repeating-linear-gradient(
                  0deg,
                  transparent,
                  transparent 30px,
                  #000000 30px,
                  #000000 31px,
                  transparent 31px,
                  transparent 40px,
                  #000000 40px,
                  #000000 41px,
                  transparent 41px,
                  transparent 50px,
                  #000000 50px,
                  #000000 51px,
                  transparent 51px,
                  transparent 60px,
                  #000000 60px,
                  #000000 61px,
                  transparent 61px,
                  transparent 70px,
                  #000000 70px,
                  #000000 71px,
                  transparent 71px,
                  transparent 120px
                )
              `
            }}
          >
            {textContent}
          </div>
        );

      case 'parchment':
        return (
          <div
            className="w-full h-full relative"
            style={{
              ...marginStyle,
              background: `linear-gradient(45deg, #f4f1e8 25%, #f7f4ec 25%, #f7f4ec 50%, #f4f1e8 50%, #f4f1e8 75%, #f7f4ec 75%, #f7f4ec)`,
              backgroundColor: '#f9f6f0',
              backgroundSize: '20px 20px',
              boxShadow: 'inset 0 0 50px rgba(139, 69, 19, 0.1)'
            }}
          >
            <div style={{ color: '#8b4513' }}>
              {textContent}
            </div>
          </div>
        );

      case 'blueprint':
        return (
          <div
            className="w-full h-full relative"
            style={{
              ...marginStyle,
              backgroundColor: '#1e3a8a',
              backgroundImage: `
                linear-gradient(to right, rgba(255,255,255,0.1) 1px, transparent 1px),
                linear-gradient(to bottom, rgba(255,255,255,0.1) 1px, transparent 1px)
              `,
              backgroundSize: '20px 20px'
            }}
          >
            {textContent}
          </div>
        );

      default: // lined
        return (
          <div
            className="w-full h-full relative bg-white"
            style={{
              ...marginStyle,
              backgroundImage: `
                linear-gradient(to bottom,
                  transparent 0px,
                  transparent 24px,
                  #e5e7eb 24px,
                  #e5e7eb 25px,
                  transparent 25px
                )`,
              backgroundSize: '100% 25px',
              backgroundRepeat: 'repeat-y'
            }}
          >
            {/* Red margin line */}
            <div
              className="absolute top-0 bottom-0 w-px bg-red-300"
              style={{ left: `${typographyConfig.marginLeft + 32}px` }}
            ></div>

            {/* Text content */}
            {textContent}
          </div>
        );
    }
  }, [
    renderTextContent,
    typographyConfig.marginTop,
    typographyConfig.marginBottom,
    typographyConfig.marginLeft,
    typographyConfig.marginRight,
    background,
    customBackground
  ]);

  return (
    <div className="min-h-screen bg-white dark:bg-black relative overflow-hidden">
      {/* Background Grid Pattern */}
      <div className="absolute inset-0 bg-[linear-gradient(to_right,#80808012_1px,transparent_1px),linear-gradient(to_bottom,#80808012_1px,transparent_1px)] bg-[size:24px_24px] dark:bg-[linear-gradient(to_right,#ffffff0f_1px,transparent_1px),linear-gradient(to_bottom,#ffffff0f_1px,transparent_1px)]"></div>
      
      {/* Radial Gradient Overlay */}
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(120,119,198,0.1),transparent_50%)] dark:bg-[radial-gradient(circle_at_50%_50%,rgba(120,119,198,0.1),transparent_50%)]"></div>
      
      <div className="relative z-10 p-4">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center mb-8"
          >
            <h1 className="text-4xl font-bold text-neutral-800 dark:text-neutral-200 mb-2 flex items-center justify-center gap-3">
              <FileText className="h-8 w-8 text-indigo-600 dark:text-indigo-400" />
              Handwritten Text to PDF
            </h1>
            <p className="text-neutral-600 dark:text-neutral-400">Transform your text into beautiful handwritten-style PDFs</p>
          </motion.div>

          {/* Enhanced Toolbar */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="mb-6"
          >
            <EnhancedToolbar
              selectedFont={selectedFont}
              onFontChange={setSelectedFont}
              fontOptions={fontOptions}
              onCustomFontUpload={handleCustomFontUpload}
              selectedBackground={background}
              onBackgroundChange={setBackground}
              customBackground={customBackground}
              onCustomBackgroundUpload={handleCustomBackgroundUpload}
              typographyConfig={typographyConfig}
              onTypographyChange={setTypographyConfig}
              showPageNumbers={showPageNumbers}
              onShowPageNumbersChange={setShowPageNumbers}
              onResetTextPosition={resetTextPosition}
              onGeneratePDF={generatePDF}
              onGenerateImage={generateImage}
              hasText={!!text.trim()}
            />
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Input Area */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <Card className="p-6 bg-white/80 dark:bg-black/40 backdrop-blur-md border border-neutral-200/20 dark:border-neutral-800/20 shadow-[0_0_24px_rgba(34,_42,_53,_0.06),_0_1px_1px_rgba(0,_0,_0,_0.05),_0_0_0_1px_rgba(34,_42,_53,_0.04),_0_0_4px_rgba(34,_42,_53,_0.08),_0_16px_68px_rgba(47,_48,_55,_0.05),_0_1px_0_rgba(255,_255,_255,_0.1)_inset] dark:shadow-[0_0_24px_rgba(0,_0,_0,_0.3)] hover:shadow-lg transition-shadow duration-300">
                <h2 className="text-xl font-semibold mb-4 text-neutral-800 dark:text-neutral-200">Write Your Text</h2>
                <Textarea
                  value={text}
                  onChange={handleTextChange}
                  placeholder="Start writing your text here... It will appear in handwritten style on the right!

LaTeX Support:
• Inline math: $x^2 + y^2 = z^2$
• Display math: $$\int_{-\infty}^{\infty} e^{-x^2} dx = \sqrt{\pi}$$
• Fractions: $\frac{a}{b}$, Greek letters: $\alpha, \beta, \gamma$"
                  className="min-h-[500px] resize-none text-base leading-relaxed bg-white/50 dark:bg-black/20 border-neutral-200/20 dark:border-neutral-700/20 focus:border-indigo-500/50 dark:focus:border-indigo-400/50"
                />
                <div className="mt-4 text-sm text-neutral-500 dark:text-neutral-400 space-y-1">
                  <div>Characters: {text.length}</div>
                  {textHasLaTeX && (
                    <div className="text-green-600 dark:text-green-400">
                      ✓ LaTeX detected and will be rendered
                    </div>
                  )}
                </div>
              </Card>
            </motion.div>

            {/* Preview Area */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <Card className="p-6 bg-white/80 dark:bg-black/40 backdrop-blur-md border border-neutral-200/20 dark:border-neutral-800/20 shadow-[0_0_24px_rgba(34,_42,_53,_0.06),_0_1px_1px_rgba(0,_0,_0,_0.05),_0_0_0_1px_rgba(34,_42,_53,_0.04),_0_0_4px_rgba(34,_42,_53,_0.08),_0_16px_68px_rgba(47,_48,_55,_0.05),_0_1px_0_rgba(255,_255,_255,_0.1)_inset] dark:shadow-[0_0_24px_rgba(0,_0,_0,_0.3)] hover:shadow-lg transition-shadow duration-300">
                {(() => {
                  const pages = memoizedSplitText;
                  const totalPages = pages.length;

                  return (
                    <>
                      <div className="flex items-center justify-between mb-4">
                        <h2 className="text-xl font-semibold text-neutral-800 dark:text-neutral-200">
                          Preview (A4 Format)
                        </h2>
                        {totalPages > 1 && (
                          <span className="text-sm text-neutral-600 dark:text-neutral-400">
                            {totalPages} page{totalPages > 1 ? 's' : ''}
                          </span>
                        )}
                      </div>

                      <div className="space-y-6 max-h-[700px] overflow-y-auto">
                        <div ref={paperRef} className="space-y-6">
                          {pages.map((pageText, index) => (
                            <MemoizedPage
                              key={index}
                              pageText={pageText}
                              index={index}
                              renderPaperBackground={renderPaperBackground}
                              totalPages={totalPages}
                              showPageNumbers={showPageNumbers}
                            />
                          ))}
                        </div>
                      </div>
                    </>
                  );
                })()}
              </Card>
            </motion.div>
          </div>

          {/* Footer */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="mt-8 text-center text-neutral-500 dark:text-neutral-400 text-sm space-y-2"
          >
            <p>Your text will be converted to PDF or PNG with handwritten-style fonts</p>
            <p>✨ Features: LaTeX math support, custom typography, ink colors, and multiple paper styles</p>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default HandwritingEditor;
