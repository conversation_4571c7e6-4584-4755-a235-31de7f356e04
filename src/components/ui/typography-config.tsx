import { FC } from 'react';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ColorPicker } from '@/components/ui/color-picker';
import { Card } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import { Type, Palette, AlignLeft, Hash, RotateCcw, Brush, Zap, Eye } from 'lucide-react';

export interface HandwritingEffects {
  shadowEnabled: boolean;
  shadowIntensity: number;
  shadowBlur: number;
  shadowOffsetX: number;
  shadowOffsetY: number;
  jitterEnabled: boolean;
  jitterIntensity: number;
  pressureVariation: boolean;
  pressureIntensity: number;
  // New messy handwriting features
  strokeThicknessVariation: boolean;
  strokeThicknessIntensity: number;
  baselineImperfection: boolean;
  baselineIntensity: number;
  spacingInconsistency: boolean;
  spacingInconsistencyIntensity: number;
}

export interface TypographyConfig {
  fontSize: number;
  lineHeight: number;
  letterSpacing: number;
  wordSpacing: number;
  textColor: string;
  marginTop: number;
  marginBottom: number;
  marginLeft: number;
  marginRight: number;
  handwritingEffects: HandwritingEffects;
}

interface TypographyConfigPanelProps {
  config: TypographyConfig;
  onChange: (config: TypographyConfig) => void;
  showPageNumbers: boolean;
  onShowPageNumbersChange: (show: boolean) => void;
  onResetTextPosition?: () => void;
  className?: string;
}

export const TypographyConfigPanel: FC<TypographyConfigPanelProps> = ({
  config,
  onChange,
  showPageNumbers,
  onShowPageNumbersChange,
  onResetTextPosition,
  className
}) => {
  const updateConfig = (key: keyof TypographyConfig, value: any) => {
    onChange({ ...config, [key]: value });
  };

  const updateHandwritingEffects = (key: keyof HandwritingEffects, value: any) => {
    onChange({
      ...config,
      handwritingEffects: {
        ...config.handwritingEffects,
        [key]: value
      }
    });
  };

  return (
    <Card className={`p-4 space-y-6 ${className}`}>
      <div className="flex items-center gap-2 mb-4">
        <Type className="h-4 w-4 text-neutral-600 dark:text-neutral-400" />
        <h3 className="text-sm font-semibold text-neutral-800 dark:text-neutral-200">
          Typography Settings
        </h3>
      </div>

      {/* Text Styling Section */}
      <div className="space-y-4">
        <div className="flex items-center gap-2 mb-3">
          <Type className="h-3 w-3 text-neutral-500 dark:text-neutral-400" />
          <h4 className="text-xs font-medium text-neutral-700 dark:text-neutral-300 uppercase tracking-wide">
            Text Styling
          </h4>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Font Size */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label className="text-xs font-medium">Font Size</Label>
              <span className="text-xs text-neutral-500 bg-neutral-100 dark:bg-neutral-800 px-2 py-1 rounded">
                {config.fontSize}px
              </span>
            </div>
            <Slider
              value={[config.fontSize]}
              onValueChange={([value]) => updateConfig('fontSize', value)}
              min={8}
              max={32}
              step={1}
              className="w-full"
            />
          </div>

          {/* Line Height */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label className="text-xs font-medium">Line Height</Label>
              <span className="text-xs text-neutral-500 bg-neutral-100 dark:bg-neutral-800 px-2 py-1 rounded">
                {config.lineHeight}
              </span>
            </div>
            <Slider
              value={[config.lineHeight]}
              onValueChange={([value]) => updateConfig('lineHeight', value)}
              min={1.0}
              max={3.0}
              step={0.1}
              className="w-full"
            />
          </div>

          {/* Letter Spacing */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label className="text-xs font-medium">Letter Spacing</Label>
              <span className="text-xs text-neutral-500 bg-neutral-100 dark:bg-neutral-800 px-2 py-1 rounded">
                {config.letterSpacing}px
              </span>
            </div>
            <Slider
              value={[config.letterSpacing]}
              onValueChange={([value]) => updateConfig('letterSpacing', value)}
              min={-2}
              max={5}
              step={0.1}
              className="w-full"
            />
          </div>

          {/* Word Spacing */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label className="text-xs font-medium">Word Spacing</Label>
              <span className="text-xs text-neutral-500 bg-neutral-100 dark:bg-neutral-800 px-2 py-1 rounded">
                {config.wordSpacing}px
              </span>
            </div>
            <Slider
              value={[config.wordSpacing]}
              onValueChange={([value]) => updateConfig('wordSpacing', value)}
              min={-5}
              max={10}
              step={0.1}
              className="w-full"
            />
          </div>
        </div>
      </div>

      {/* Ink Color Section */}
      <div className="space-y-4">
        <div className="flex items-center gap-2 mb-3">
          <Palette className="h-3 w-3 text-neutral-500 dark:text-neutral-400" />
          <h4 className="text-xs font-medium text-neutral-700 dark:text-neutral-300 uppercase tracking-wide">
            Ink Color
          </h4>
        </div>

        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Label className="text-xs font-medium">Text Color</Label>
            <div
              className="w-4 h-4 rounded border border-neutral-300 dark:border-neutral-600"
              style={{ backgroundColor: config.textColor }}
            />
          </div>
          <ColorPicker
            value={config.textColor}
            onChange={(color) => updateConfig('textColor', color)}
            className="w-full"
          />
        </div>
      </div>

      {/* Page Layout Section */}
      <div className="space-y-4">
        <div className="flex items-center gap-2 mb-3">
          <AlignLeft className="h-3 w-3 text-neutral-500 dark:text-neutral-400" />
          <h4 className="text-xs font-medium text-neutral-700 dark:text-neutral-300 uppercase tracking-wide">
            Page Layout
          </h4>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Top Margin */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label className="text-xs font-medium">Top Margin</Label>
              <span className="text-xs text-neutral-500 bg-neutral-100 dark:bg-neutral-800 px-2 py-1 rounded">
                {config.marginTop}px
              </span>
            </div>
            <Slider
              value={[config.marginTop]}
              onValueChange={([value]) => updateConfig('marginTop', value)}
              min={0}
              max={100}
              step={1}
              className="w-full"
            />
          </div>

          {/* Bottom Margin */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label className="text-xs font-medium">Bottom Margin</Label>
              <span className="text-xs text-neutral-500 bg-neutral-100 dark:bg-neutral-800 px-2 py-1 rounded">
                {config.marginBottom}px
              </span>
            </div>
            <Slider
              value={[config.marginBottom]}
              onValueChange={([value]) => updateConfig('marginBottom', value)}
              min={0}
              max={100}
              step={1}
              className="w-full"
            />
          </div>

          {/* Left Margin */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label className="text-xs font-medium">Left Margin</Label>
              <span className="text-xs text-neutral-500 bg-neutral-100 dark:bg-neutral-800 px-2 py-1 rounded">
                {config.marginLeft}px
              </span>
            </div>
            <Slider
              value={[config.marginLeft]}
              onValueChange={([value]) => updateConfig('marginLeft', value)}
              min={0}
              max={100}
              step={1}
              className="w-full"
            />
          </div>

          {/* Right Margin */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label className="text-xs font-medium">Right Margin</Label>
              <span className="text-xs text-neutral-500 bg-neutral-100 dark:bg-neutral-800 px-2 py-1 rounded">
                {config.marginRight}px
              </span>
            </div>
            <Slider
              value={[config.marginRight]}
              onValueChange={([value]) => updateConfig('marginRight', value)}
              min={0}
              max={100}
              step={1}
              className="w-full"
            />
          </div>
        </div>
      </div>

      {/* Handwriting Effects Section */}
      <div className="space-y-4">
        <div className="flex items-center gap-2 mb-3">
          <Brush className="h-3 w-3 text-neutral-500 dark:text-neutral-400" />
          <h4 className="text-xs font-medium text-neutral-700 dark:text-neutral-300 uppercase tracking-wide">
            Realistic Handwriting Effects
          </h4>
        </div>

        {/* Shadow & Depth Effects */}
        <div className="space-y-4 p-3 bg-neutral-50 dark:bg-neutral-800/50 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <Eye className="h-3 w-3 text-neutral-500 dark:text-neutral-400" />
            <h5 className="text-xs font-medium text-neutral-700 dark:text-neutral-300">
              Shadow & Depth
            </h5>
          </div>

          {/* Shadow Toggle */}
          <div className="flex items-center justify-between">
            <div>
              <Label className="text-xs font-medium">Enable Shadow</Label>
              <p className="text-xs text-neutral-500">Add depth to handwriting</p>
            </div>
            <Switch
              checked={config.handwritingEffects.shadowEnabled}
              onCheckedChange={(value) => updateHandwritingEffects('shadowEnabled', value)}
            />
          </div>

          {config.handwritingEffects.shadowEnabled && (
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-3">
              {/* Shadow Intensity */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label className="text-xs font-medium">Intensity</Label>
                  <span className="text-xs text-neutral-500 bg-neutral-100 dark:bg-neutral-800 px-2 py-1 rounded">
                    {config.handwritingEffects.shadowIntensity}%
                  </span>
                </div>
                <Slider
                  value={[config.handwritingEffects.shadowIntensity]}
                  onValueChange={([value]) => updateHandwritingEffects('shadowIntensity', value)}
                  min={0}
                  max={100}
                  step={5}
                  className="w-full"
                />
              </div>

              {/* Shadow Blur */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label className="text-xs font-medium">Blur</Label>
                  <span className="text-xs text-neutral-500 bg-neutral-100 dark:bg-neutral-800 px-2 py-1 rounded">
                    {config.handwritingEffects.shadowBlur}px
                  </span>
                </div>
                <Slider
                  value={[config.handwritingEffects.shadowBlur]}
                  onValueChange={([value]) => updateHandwritingEffects('shadowBlur', value)}
                  min={0}
                  max={10}
                  step={0.5}
                  className="w-full"
                />
              </div>

              {/* Shadow Offset X */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label className="text-xs font-medium">Offset X</Label>
                  <span className="text-xs text-neutral-500 bg-neutral-100 dark:bg-neutral-800 px-2 py-1 rounded">
                    {config.handwritingEffects.shadowOffsetX}px
                  </span>
                </div>
                <Slider
                  value={[config.handwritingEffects.shadowOffsetX]}
                  onValueChange={([value]) => updateHandwritingEffects('shadowOffsetX', value)}
                  min={-5}
                  max={5}
                  step={0.5}
                  className="w-full"
                />
              </div>

              {/* Shadow Offset Y */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label className="text-xs font-medium">Offset Y</Label>
                  <span className="text-xs text-neutral-500 bg-neutral-100 dark:bg-neutral-800 px-2 py-1 rounded">
                    {config.handwritingEffects.shadowOffsetY}px
                  </span>
                </div>
                <Slider
                  value={[config.handwritingEffects.shadowOffsetY]}
                  onValueChange={([value]) => updateHandwritingEffects('shadowOffsetY', value)}
                  min={-5}
                  max={5}
                  step={0.5}
                  className="w-full"
                />
              </div>
            </div>
          )}
        </div>

        {/* Line Irregularities & Jitter */}
        <div className="space-y-4 p-3 bg-neutral-50 dark:bg-neutral-800/50 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <Zap className="h-3 w-3 text-neutral-500 dark:text-neutral-400" />
            <h5 className="text-xs font-medium text-neutral-700 dark:text-neutral-300">
              Line Irregularities
            </h5>
          </div>

          {/* Jitter Toggle */}
          <div className="flex items-center justify-between">
            <div>
              <Label className="text-xs font-medium">Enable Jitter</Label>
              <p className="text-xs text-neutral-500">Add natural imperfections</p>
            </div>
            <Switch
              checked={config.handwritingEffects.jitterEnabled}
              onCheckedChange={(value) => updateHandwritingEffects('jitterEnabled', value)}
            />
          </div>

          {config.handwritingEffects.jitterEnabled && (
            <div className="space-y-4 mt-3">
              {/* Jitter Intensity */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label className="text-xs font-medium">Jitter Intensity</Label>
                  <span className="text-xs text-neutral-500 bg-neutral-100 dark:bg-neutral-800 px-2 py-1 rounded">
                    {config.handwritingEffects.jitterIntensity}%
                  </span>
                </div>
                <Slider
                  value={[config.handwritingEffects.jitterIntensity]}
                  onValueChange={([value]) => updateHandwritingEffects('jitterIntensity', value)}
                  min={0}
                  max={100}
                  step={5}
                  className="w-full"
                />
              </div>
            </div>
          )}

          {/* Pressure Variation Toggle */}
          <div className="flex items-center justify-between">
            <div>
              <Label className="text-xs font-medium">Pressure Variation</Label>
              <p className="text-xs text-neutral-500">Simulate pen pressure changes</p>
            </div>
            <Switch
              checked={config.handwritingEffects.pressureVariation}
              onCheckedChange={(value) => updateHandwritingEffects('pressureVariation', value)}
            />
          </div>

          {config.handwritingEffects.pressureVariation && (
            <div className="space-y-2 mt-3">
              <div className="flex items-center justify-between">
                <Label className="text-xs font-medium">Pressure Intensity</Label>
                <span className="text-xs text-neutral-500 bg-neutral-100 dark:bg-neutral-800 px-2 py-1 rounded">
                  {config.handwritingEffects.pressureIntensity}%
                </span>
              </div>
              <Slider
                value={[config.handwritingEffects.pressureIntensity]}
                onValueChange={([value]) => updateHandwritingEffects('pressureIntensity', value)}
                min={0}
                max={100}
                step={5}
                className="w-full"
              />
            </div>
          )}
        </div>

        {/* Messy Handwriting Features */}
        <div className="space-y-4 p-3 bg-neutral-50 dark:bg-neutral-800/50 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <Brush className="h-3 w-3 text-neutral-500 dark:text-neutral-400" />
            <h5 className="text-xs font-medium text-neutral-700 dark:text-neutral-300">
              Messy Handwriting
            </h5>
          </div>

          {/* Stroke Thickness Variation */}
          <div className="flex items-center justify-between">
            <div>
              <Label className="text-xs font-medium">Stroke Thickness</Label>
              <p className="text-xs text-neutral-500">Dynamic line weight changes</p>
            </div>
            <Switch
              checked={config.handwritingEffects.strokeThicknessVariation}
              onCheckedChange={(value) => updateHandwritingEffects('strokeThicknessVariation', value)}
            />
          </div>

          {config.handwritingEffects.strokeThicknessVariation && (
            <div className="space-y-2 mt-3">
              <div className="flex items-center justify-between">
                <Label className="text-xs font-medium">Thickness Intensity</Label>
                <span className="text-xs text-neutral-500 bg-neutral-100 dark:bg-neutral-800 px-2 py-1 rounded">
                  {config.handwritingEffects.strokeThicknessIntensity}%
                </span>
              </div>
              <Slider
                value={[config.handwritingEffects.strokeThicknessIntensity]}
                onValueChange={([value]) => updateHandwritingEffects('strokeThicknessIntensity', value)}
                min={0}
                max={100}
                step={5}
                className="w-full"
              />
            </div>
          )}

          {/* Baseline Imperfection */}
          <div className="flex items-center justify-between">
            <div>
              <Label className="text-xs font-medium">Baseline Imperfection</Label>
              <p className="text-xs text-neutral-500">Text doesn't sit perfectly straight</p>
            </div>
            <Switch
              checked={config.handwritingEffects.baselineImperfection}
              onCheckedChange={(value) => updateHandwritingEffects('baselineImperfection', value)}
            />
          </div>

          {config.handwritingEffects.baselineImperfection && (
            <div className="space-y-2 mt-3">
              <div className="flex items-center justify-between">
                <Label className="text-xs font-medium">Baseline Intensity</Label>
                <span className="text-xs text-neutral-500 bg-neutral-100 dark:bg-neutral-800 px-2 py-1 rounded">
                  {config.handwritingEffects.baselineIntensity}%
                </span>
              </div>
              <Slider
                value={[config.handwritingEffects.baselineIntensity]}
                onValueChange={([value]) => updateHandwritingEffects('baselineIntensity', value)}
                min={0}
                max={100}
                step={5}
                className="w-full"
              />
            </div>
          )}

          {/* Spacing Inconsistency */}
          <div className="flex items-center justify-between">
            <div>
              <Label className="text-xs font-medium">Spacing Inconsistency</Label>
              <p className="text-xs text-neutral-500">Non-uniform letter and word spacing</p>
            </div>
            <Switch
              checked={config.handwritingEffects.spacingInconsistency}
              onCheckedChange={(value) => updateHandwritingEffects('spacingInconsistency', value)}
            />
          </div>

          {config.handwritingEffects.spacingInconsistency && (
            <div className="space-y-2 mt-3">
              <div className="flex items-center justify-between">
                <Label className="text-xs font-medium">Spacing Intensity</Label>
                <span className="text-xs text-neutral-500 bg-neutral-100 dark:bg-neutral-800 px-2 py-1 rounded">
                  {config.handwritingEffects.spacingInconsistencyIntensity}%
                </span>
              </div>
              <Slider
                value={[config.handwritingEffects.spacingInconsistencyIntensity]}
                onValueChange={([value]) => updateHandwritingEffects('spacingInconsistencyIntensity', value)}
                min={0}
                max={100}
                step={5}
                className="w-full"
              />
            </div>
          )}
        </div>
      </div>

      {/* Document Options Section */}
      <div className="space-y-4">
        <div className="flex items-center gap-2 mb-3">
          <Hash className="h-3 w-3 text-neutral-500 dark:text-neutral-400" />
          <h4 className="text-xs font-medium text-neutral-700 dark:text-neutral-300 uppercase tracking-wide">
            Document Options
          </h4>
        </div>

        <div className="grid grid-cols-1 gap-4">
          {/* Page Numbers */}
          <div className="flex items-center justify-between p-3 bg-neutral-50 dark:bg-neutral-800/50 rounded-lg">
            <div className="flex items-center gap-2">
              <Hash className="h-4 w-4 text-neutral-600 dark:text-neutral-400" />
              <div>
                <Label className="text-xs font-medium">Page Numbers</Label>
                <p className="text-xs text-neutral-500">Show page numbers on multi-page documents</p>
              </div>
            </div>
            <Switch
              checked={showPageNumbers}
              onCheckedChange={onShowPageNumbersChange}
            />
          </div>

          {/* Text Position Reset */}
          {onResetTextPosition && (
            <div className="flex items-center justify-between p-3 bg-neutral-50 dark:bg-neutral-800/50 rounded-lg">
              <div className="flex items-center gap-2">
                <RotateCcw className="h-4 w-4 text-neutral-600 dark:text-neutral-400" />
                <div>
                  <Label className="text-xs font-medium">Text Position</Label>
                  <p className="text-xs text-neutral-500">
                    Drag text in preview to reposition
                  </p>
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={onResetTextPosition}
                className="h-8 px-3 text-xs"
              >
                Reset
              </Button>
            </div>
          )}
        </div>
      </div>
    </Card>
  );
};
