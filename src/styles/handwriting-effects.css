/* Handwriting Effects CSS */

/* Subtle breathing animation for pressure variation */
@keyframes handwriting-breathe {
  0%, 100% { 
    opacity: 1;
    font-weight: inherit;
  }
  50% { 
    opacity: 0.95;
    font-weight: calc(var(--base-weight, 400) + 50);
  }
}

/* Subtle shake animation for jitter effect */
@keyframes handwriting-jitter {
  0%, 100% { 
    transform: translate(0, 0) rotate(0deg);
  }
  25% { 
    transform: translate(0.5px, -0.3px) rotate(0.2deg);
  }
  50% { 
    transform: translate(-0.3px, 0.5px) rotate(-0.1deg);
  }
  75% { 
    transform: translate(0.2px, 0.2px) rotate(0.1deg);
  }
}

/* Ink flow animation for more realistic writing */
@keyframes ink-flow {
  0% { 
    opacity: 0.8;
    filter: blur(0.2px);
  }
  50% { 
    opacity: 1;
    filter: blur(0px);
  }
  100% { 
    opacity: 0.95;
    filter: blur(0.1px);
  }
}

/* Classes for applying effects */
.handwriting-pressure-variation {
  animation: handwriting-breathe 4s ease-in-out infinite;
}

.handwriting-jitter-subtle {
  animation: handwriting-jitter 6s ease-in-out infinite;
}

.handwriting-ink-flow {
  animation: ink-flow 3s ease-in-out infinite;
}

/* Enhanced shadow effects */
.handwriting-shadow-light {
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.handwriting-shadow-medium {
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
}

.handwriting-shadow-heavy {
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

/* Realistic paper texture overlay */
.handwriting-paper-texture::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(0, 0, 0, 0.01) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(0, 0, 0, 0.01) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(0, 0, 0, 0.005) 0%, transparent 30%);
  background-size: 8px 8px, 12px 12px, 4px 4px;
  pointer-events: none;
  z-index: 1;
}

/* Character-level jitter variations */
.handwriting-char-jitter-1 {
  transform: rotate(0.5deg) translate(0.2px, -0.1px);
}

.handwriting-char-jitter-2 {
  transform: rotate(-0.3deg) translate(-0.1px, 0.2px);
}

.handwriting-char-jitter-3 {
  transform: rotate(0.2deg) translate(0.1px, 0.1px);
}

.handwriting-char-jitter-4 {
  transform: rotate(-0.1deg) translate(-0.2px, -0.1px);
}

.handwriting-char-jitter-5 {
  transform: rotate(0.4deg) translate(0.1px, -0.2px);
}

/* Pressure variation classes */
.handwriting-pressure-light {
  opacity: 0.85;
  font-weight: 300;
}

.handwriting-pressure-medium {
  opacity: 0.95;
  font-weight: 400;
}

.handwriting-pressure-heavy {
  opacity: 1;
  font-weight: 500;
}
