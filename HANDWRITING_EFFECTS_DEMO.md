# Handwriting Effects Demo

## New Realistic Handwriting Features Added

### 1. Shadow and Depth Effects ✨
- **Shadow Toggle**: Enable/disable text shadows for depth
- **Shadow Intensity**: Control shadow opacity (0-100%)
- **Shadow Blur**: Adjust shadow blur radius (0-10px)
- **Shadow Offset X/Y**: Position shadow horizontally and vertically (-5 to 5px)

### 2. Line Irregularities and Jitter 🎯
- **Jitter Toggle**: Enable natural handwriting imperfections
- **Jitter Intensity**: Control the amount of character variation (0-100%)
- **Per-Character Randomization**: Each character gets unique micro-rotations and translations
- **Seeded Randomness**: Consistent jitter patterns using mathematical sequences

### 3. Pressure Variation 📝
- **Pressure Toggle**: Simulate pen pressure changes
- **Pressure Intensity**: Control opacity and weight variations (0-100%)
- **Dynamic Font Weight**: Characters vary between 400-600 weight
- **Opacity Variation**: Subtle opacity changes (0.8-1.2 range)

## How to Test the Features

1. **Open the app** at http://localhost:8081
2. **Write some text** in the input area
3. **Open Typography Settings** in the toolbar
4. **Scroll to "Realistic Handwriting Effects"** section
5. **Try different combinations**:
   - Enable Shadow with medium intensity
   - Turn on Jitter with 30-50% intensity
   - Add Pressure Variation with 20-40% intensity

## Technical Implementation

### CSS-Based Effects
- Text shadows for depth perception
- Per-character transforms for natural irregularities
- Dynamic font-weight and opacity variations

### Mathematical Approach
- Golden ratio-based seeding for consistent randomness
- Trigonometric functions for smooth variations
- Realistic parameter ranges based on actual handwriting analysis

### Performance Optimizations
- Memoized calculations to prevent unnecessary re-renders
- Efficient per-character rendering only when needed
- Debounced localStorage saves

## Example Settings for Different Styles

### Casual Handwriting
- Shadow: Enabled, 20% intensity, 1px blur, 1px offset
- Jitter: Enabled, 25% intensity
- Pressure: Enabled, 15% intensity

### Formal Writing
- Shadow: Enabled, 10% intensity, 0.5px blur, 0.5px offset
- Jitter: Disabled or 10% intensity
- Pressure: Enabled, 10% intensity

### Artistic/Expressive
- Shadow: Enabled, 40% intensity, 3px blur, 2px offset
- Jitter: Enabled, 60% intensity
- Pressure: Enabled, 50% intensity

## Browser Compatibility
- Works in all modern browsers
- CSS transforms and text-shadow support required
- Graceful degradation for older browsers
